<template>
  <Dialog :title="dialogTitle" v-model="dialogVisible" :width="1000">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="auto"
      v-loading="formLoading"
    >
      <el-row :gutter="20">
        <el-col :span="8">
          <el-form-item label="单号" prop="orderNo">
            <el-input v-model="formData.orderNo" placeholder="保存时自动生成" disabled/>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="业务类型" prop="bizType">
            <el-select v-model="formData.bizType" placeholder="请选择业务类型" disabled>
              <el-option
                v-for="item in inventory_transaction_type"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="来源类型" prop="sourceType">
            <el-select v-model="formData.sourceType" placeholder="请选择来源类型">
              <el-option 
              v-for="item in sale_out_source"
              :key="item.value" 
              :label="item.label" 
              :value="item.value" 
              />
            </el-select>
          </el-form-item>
        </el-col>
        <!-- <el-col :span="8">
          <el-form-item label="来源单ID" prop="sourceId">
            <el-input v-model="formData.sourceId" placeholder="请输入来源单ID" />
          </el-form-item>
        </el-col> -->
        <el-col :span="8">
          <el-form-item label="来源单" prop="sourceNo">
            <ScrollSelect
              v-model="formData.sourceNo"
              :load-method="loadDeliveryNotices"
              label-key="orderNoWithCustomer"
              value-key="orderNo"
              query-key="orderNo"
              :default-value="deliveryNoticeDefaultValue"
              :extra-params="deliveryNoticeExtraParams"
              @change="onDeliveryNoticeChange"
              placeholder="请选择来源单"
              :key="formData.sourceNo"
            />
          </el-form-item>
        </el-col>
        <!-- <el-col :span="8">
          <el-form-item label="交易对象ID" prop="objectId">
            <el-input v-model="formData.objectId" placeholder="请输入交易对象ID" />
          </el-form-item>
        </el-col> -->
        <el-col :span="8">
          <el-form-item label="客户名称" prop="objectName">
            <el-input v-model="formData.objectName" placeholder="请选择客户名称" />
          </el-form-item>
        </el-col>
<!--        <el-col :span="8">-->
<!--          <el-form-item label="交易对象订单号" prop="objectOrderNo">-->
<!--            <el-input v-model="formData.objectOrderNo" placeholder="请输入交易对象订单号" />-->
<!--          </el-form-item>-->
<!--        </el-col>-->
        <el-col :span="8">
          <el-form-item label="交易日期" prop="date">
            <el-date-picker
              v-model="formData.date"
              type="date"
              value-format="x"
              placeholder="选择交易日期"
            />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="仓库名称" prop="warehouseId">
            <el-tree-select v-model="formData.warehouseId" :data="warehouseList" placeholder="请选择仓库"/>
          </el-form-item>
        </el-col>
        <!-- <el-col :span="8">
          <el-form-item label="科目ID" prop="accountId">
            <el-input v-model="formData.accountId" placeholder="请输入科目ID" />
          </el-form-item>
        </el-col> -->
        <el-col :span="8">
          <el-form-item label="摘要" prop="note">
            <el-input v-model="formData.note" placeholder="请输入摘要" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="备注" prop="remark">
            <el-input v-model="formData.remark" placeholder="请输入备注" />
          </el-form-item>
        </el-col>
        <!-- <el-col :span="8">
          <el-form-item label="审批状态" prop="approveStatus">
            <el-radio-group v-model="formData.approveStatus">
              <el-radio 
              v-for="item in approve_status" 
              :key="item.value" 
              :label="item.label" 
              :value="item.value" 
              />
            </el-radio-group>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="审批单号" prop="approveNo">
            <el-input v-model="formData.approveNo" placeholder="请输入审批单号" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="审批人ID" prop="approverId">
            <el-input v-model="formData.approverId" placeholder="请输入审批人ID" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="审批人" prop="approverName">
            <el-input v-model="formData.approverName" placeholder="请输入审批人" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="审批时间" prop="approveDate">
            <el-date-picker
              v-model="formData.approveDate"
              type="date"
              value-format="x"
              placeholder="选择审批时间"
            />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="部门ID" prop="deptId">
            <el-input v-model="formData.deptId" placeholder="请输入部门ID" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="业务员ID" prop="empId">
            <el-input v-model="formData.empId" placeholder="请输入业务员ID" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="管理员ID" prop="managerId">
            <el-input v-model="formData.managerId" placeholder="请输入管理员ID" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="管理员1ID" prop="manger1Id">
            <el-input v-model="formData.manger1Id" placeholder="请输入管理员1ID" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="记账ID" prop="accountantId">
            <el-input v-model="formData.accountantId" placeholder="请输入记账ID" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="检验员ID" prop="checkerId">
            <el-input v-model="formData.checkerId" placeholder="请输入检验员ID" />
          </el-form-item>
        </el-col> -->
      </el-row>
    </el-form>
    <!-- 子表的表单 -->
    <el-tabs v-model="subTabsName">
      <el-tab-pane label="销售出库明细" name="deliveryReceiptDetail">
        <DeliveryReceiptDetailForm ref="deliveryReceiptDetailFormRef" :biz-order-id="formData.id" :warehouse-id="formData.warehouseId" />
      </el-tab-pane>
    </el-tabs>
    <template #footer>
      <el-button @click="submitForm" type="primary" :disabled="formLoading">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>
<script setup lang="ts">
import { nextTick } from 'vue'
import { DeliveryReceiptApi, DeliveryReceiptVO } from '@/api/scm/inventory/deliveryreceipt'
import DeliveryReceiptDetailForm from './components/DeliveryReceiptDetailForm.vue'
import { DICT_TYPE,getStrDictOptions } from '@/utils/dict'
import { WarehouseApi } from '@/api/scm/inventory/warehouse'
import { handleTree } from '@/utils/tree'
import { DeliveryNoticeApi, DeliveryNoticeVO } from '@/api/scm/sale/deliverynotice'
import ScrollSelect from '@/components/ScrollSelect/index.vue'
/** 销售出库 表单 */
defineOptions({ name: 'DeliveryReceiptForm' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗
const sale_out_source = getStrDictOptions(DICT_TYPE.SALE_OUT_SOURCE)
const inventory_transaction_type = getStrDictOptions(DICT_TYPE.INVENTORY_TRANSACTION_TYPE)
const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改
const warehouseList = ref<any[]>([])

// 发货通知单选择器相关
const deliveryNoticeDefaultValue = ref({})
const deliveryNoticeExtraParams = ref({})
const formData = ref({
  id: undefined as number | undefined,
  orderNo: undefined as string | undefined,
  bizType: 'sale_delivery',
  sourceType: undefined as string | undefined,
  sourceId: undefined as number | undefined,
  sourceNo: undefined as string | undefined,
  objectId: undefined as number | undefined,
  objectName: undefined as string | undefined,
  objectOrderNo: undefined as string | undefined,
  date: undefined as Date | undefined,
  warehouseId: undefined as number | undefined,
  accountId: undefined as number | undefined,
  note: undefined as string | undefined,
  remark: undefined as string | undefined,
  approveStatus: undefined as number | undefined,
  approveNo: undefined as string | undefined,
  approverId: undefined as number | undefined,
  approverName: undefined as string | undefined,
  approveDate: undefined as Date | undefined,
  deptId: undefined as number | undefined,
  empId: undefined as number | undefined,
  managerId: undefined as number | undefined,
  manger1Id: undefined as number | undefined,
  accountantId: undefined as number | undefined,
  checkerId: undefined as number | undefined,
})
const formRules = reactive({
})
const formRef = ref() // 表单 Ref

/** 子表的表单 */
const subTabsName = ref('deliveryReceiptDetail')
const deliveryReceiptDetailFormRef = ref()

/** 打开弹窗 */
const open = async (type: string, id?: number, sourceData?: any) => {
  dialogVisible.value = true
  dialogTitle.value = t('action.' + type)
  formType.value = type
  resetForm()
  await initWarehouseList()
  console.log('sourceData:', sourceData)
  // 修改时，设置数据
  if (id) {
    formLoading.value = true
    try {
      formData.value = await DeliveryReceiptApi.getDeliveryReceipt(id)
    } finally {
      formLoading.value = false
    }
  }
  
  // 如果有来源数据，填充表单
  if (sourceData && type === 'create') {
    formData.value.sourceType = sourceData.sourceType
    formData.value.sourceId = sourceData.sourceId
    formData.value.sourceNo = sourceData.sourceNo
    formData.value.objectName = sourceData.objectName
    formData.value.objectOrderNo = sourceData.objectOrderNo
    
    // 如果有发货日期，设置交易日期
    if (sourceData.deliveryDate) {
      formData.value.date = sourceData.deliveryDate
    }
    
    // 如果有明细数据，自动加载
    if (sourceData.details && sourceData.details.length > 0) {
      nextTick(() => {
        if (deliveryReceiptDetailFormRef.value && deliveryReceiptDetailFormRef.value.setData) {
          // 转换发货通知单明细为出库明细格式
          console.log(sourceData.details)
          const deliveryDetails = sourceData.details.map((detail: any, index: number) => ({
            id: undefined, // 新增时ID为空
            num: index + 1, // 序号
            bizOrderId: formData.value.id, // 销售出库单ID
            bizOrderNo: formData.value.orderNo, // 销售出库单号
            warehouseId: formData.value.warehouseId, // 仓库ID
            locationId: undefined, // 库位ID
            materialId: detail.materialId || detail.productId, // 物料ID
            materialName: detail.materialName || detail.productName, // 物料名称
            materialCode: detail.materialCode || detail.productCode, // 物料编号
            unit: detail.unit || detail.materialUnit, // 单位
            unitPrice: detail.materialUnitPrice || detail.unitPrice || 0, // 单价
            amount: detail.totalAmount || detail.amount || 0, // 金额
            remark: detail.remark || '', // 明细备注
            plannedQuantity: detail.materialQuantity || detail.quantity || 0, // 应发数量
            fulfilledQuantity: 0, // 实发数量（初始为0，用户填写）
            standardPlannedQuantity: detail.standardQuantity || 0, // 基本单位应发数量
            standardFulfilledQuantity: 0, // 基本单位实发数量（初始为0）
            standardUnit: detail.standardUnitId || '', // 基本单位ID
            taxPrice: detail.materialUnitPrice || detail.unitPrice || 0, // 含税单价
            taxAmount: detail.totalAmount || detail.taxAmount || 0, // 含税金额
            invoiceQuantity: 0, // 开票数量
            invoiceAmount: 0, // 开票金额
            standardInvoiceQuantity: 0, // 开票基本数量
            effictiveDate: undefined, // 生产日期
            expiryDate: undefined, // 失效日期
            note: detail.note || '', // 说明
            sourceId: sourceData.sourceId, // 源单ID
            sourceNo: sourceData.sourceNo, // 源单单号
            sourceType: sourceData.sourceType,
            sourceDetailId: detail.sourceDetailId, // 源单明细ID
            batchNo: detail.batchNo || '', // 批号
            costObjectId: undefined, // 成本对象编码
            costObjectName: undefined, // 成本对象名称
            accountingVoucherNumber: undefined, // 记账凭证号
          }))
          
          deliveryReceiptDetailFormRef.value.setData(deliveryDetails)
        }
      })
    }
  }
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  await formRef.value.validate()
  // 校验子表单
  try {
    await deliveryReceiptDetailFormRef.value.validate()
  } catch (e) {
    subTabsName.value = 'deliveryReceiptDetail'
    return
  }
  // 提交请求
  formLoading.value = true
  try {
    const data = formData.value as unknown as DeliveryReceiptVO
    // 拼接子表的数据
    data.deliveryReceiptDetails = deliveryReceiptDetailFormRef.value.getData()
    if (formType.value === 'create') {
      await DeliveryReceiptApi.createDeliveryReceipt(data)
      message.success(t('common.createSuccess'))
    } else {
      await DeliveryReceiptApi.updateDeliveryReceipt(data)
      message.success(t('common.updateSuccess'))
    }
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

/** 加载发货通知单数据 */
const loadDeliveryNotices = async (params: any) => {
  try {
    const response = await DeliveryNoticeApi.getDeliveryNoticePage({
      pageNo: params.page || 1,
      pageSize: params.size || 20,
      orderNo: params.query || '',
      ...deliveryNoticeExtraParams.value
    })

    const notices = response.list || []

    // 为每个发货通知单添加显示标签（订单号 + 客户名称）
    const processedNotices = notices.map((notice: DeliveryNoticeVO) => ({
      ...notice,
      orderNoWithCustomer: `${notice.orderNo} - ${notice.customerName || '未知客户'}`
    }))

    return {
      list: processedNotices,
      total: response.total || 0
    }
  } catch (error) {
    return {
      list: [],
      total: 0
    }
  }
}

/** 发货通知单选择变化 */
const onDeliveryNoticeChange = async (value: any, selectedNotice: DeliveryNoticeVO | null) => {
  if (selectedNotice) {
    // 设置来源单相关信息
    formData.value.sourceId = selectedNotice.id
    formData.value.sourceNo = selectedNotice.orderNo
    formData.value.objectId = selectedNotice.customerId
    formData.value.objectName = selectedNotice.customerName
    formData.value.objectOrderNo = selectedNotice.orderNo
    formData.value.sourceType = 'delivery_notice'
    // 加载发货通知单明细并添加到出库明细中
    const noticeIdToUse = selectedNotice.id
    if (noticeIdToUse) {
      await loadDeliveryNoticeDetails(noticeIdToUse)
    }
  } else if (value) {
    // 如果只有value没有selectedNotice，只设置订单编号
    formData.value.sourceNo = value
  } else {
    // 清空相关字段
    formData.value.sourceId = undefined
    formData.value.sourceNo = undefined
    formData.value.objectId = undefined
    formData.value.objectName = undefined
    formData.value.objectOrderNo = undefined

    // 清空明细数据
    if (deliveryReceiptDetailFormRef.value && deliveryReceiptDetailFormRef.value.setData) {
      deliveryReceiptDetailFormRef.value.setData([])
    }
  }
}

/** 加载发货通知单明细并添加到销售出库明细中 */
const loadDeliveryNoticeDetails = async (noticeId: number) => {
  try {
    // 确保noticeId是数字类型
    const noticeIdNumber = Number(noticeId)
    if (isNaN(noticeIdNumber)) {
      return
    }

    // 获取发货通知单明细
    const response = await DeliveryNoticeApi.getDeliveryNoticeDetailList(noticeIdNumber)

    // 检查返回的数据结构
    const detailList = Array.isArray(response) ? response : []

    if (detailList && detailList.length > 0) {
      // 将发货通知单明细转换为销售出库明细格式
      const deliveryDetails = detailList.map((detail: any, index: number) => ({
        id: undefined, // 新增时ID为空
        num: index + 1, // 序号
        bizOrderId: formData.value.id, // 销售出库单ID
        bizOrderNo: formData.value.orderNo, // 销售出库单号
        warehouseId: formData.value.warehouseId, // 仓库ID
        locationId: undefined, // 库位ID
        materialId: detail.materialId || detail.productId, // 物料ID
        materialName: detail.materialName || detail.productName, // 物料名称
        materialCode: detail.materialCode || detail.productCode, // 物料编号
        unit: detail.unit || detail.materialUnit, // 单位
        unitPrice: detail.materialUnitPrice || detail.unitPrice || 0, // 含税单价
        amount: detail.totalAmount || detail.amount || 0, // 金额
        remark: detail.remark || '', // 明细备注
        plannedQuantity: detail.materialQuantity || detail.quantity || 0, // 应发数量
        fulfilledQuantity: 0, // 实发数量（初始为0，用户填写）
        standardPlannedQuantity: detail.standardQuantity || 0, // 基本单位应发数量
        standardFulfilledQuantity: 0, // 基本单位实发数量（初始为0）
        standardUnit: detail.standardUnitId || '', // 基本单位ID
        taxPrice: detail.materialUnitPrice || detail.unitPrice || 0, // 含税单价
        taxAmount: detail.totalAmount || detail.taxAmount || 0, // 含税金额
        invoiceQuantity: 0, // 开票数量
        invoiceAmount: 0, // 开票金额
        standardInvoiceQuantity: 0, // 开票基本数量
        effictiveDate: undefined, // 生产日期
        expiryDate: undefined, // 失效日期
        note: detail.note || '', // 说明
        sourceId: detail.id, // 源单ID（发货通知单明细ID）
        sourceNo: formData.value.sourceNo, // 源单单号（使用主单的sourceNo）
        sourceType: formData.value.sourceType,
        sourceDetailId: detail.id, // 源单明细ID
        batchNo: detail.batchNo || '', // 批号
        costObjectId: undefined, // 成本对象编码
        costObjectName: undefined, // 成本对象名称
        accountingVoucherNumber: undefined, // 记账凭证号
      }))

      // 将明细数据设置到子表单中
      if (deliveryReceiptDetailFormRef.value && deliveryReceiptDetailFormRef.value.setData) {
        deliveryReceiptDetailFormRef.value.setData(deliveryDetails)
      }
    } else {
      // 当来源单明细为空时，清空子表单的明细数据
      if (deliveryReceiptDetailFormRef.value && deliveryReceiptDetailFormRef.value.setData) {
        deliveryReceiptDetailFormRef.value.setData([])
      }
    }
  } catch (error: any) {
    // 静默处理错误，避免影响用户体验
  }
}

//初始化仓库数据
const initWarehouseList = async () => {
  const res = await WarehouseApi.getWarehouseList({
    pageNo:1,
    pageSize:100
  })
  const formatTreeData = (list: any[]) => {
    return list.map(item => {
      const node:any = {
        id: item.id,
        label: item.name,
        value: item.id,
        parentId: item.parentId
      }

      if (item.children && item.children.length > 0) {
        node.children = formatTreeData(item.children)
      }

      return node
    })
  }
  const warehouseTree = handleTree(res, 'id', 'parentId')
  warehouseList.value = formatTreeData(warehouseTree)
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    id: undefined as number | undefined,
    orderNo: undefined as string | undefined,
    bizType: 'sale_delivery',
    sourceType: undefined as string | undefined,
    sourceId: undefined as number | undefined,
    sourceNo: undefined as string | undefined,
    objectId: undefined as number | undefined,
    objectName: undefined as string | undefined,
    objectOrderNo: undefined as string | undefined,
    date: undefined as Date | undefined,
    warehouseId: undefined as number | undefined,
    accountId: undefined as number | undefined,
    note: undefined as string | undefined,
    remark: undefined as string | undefined,
    approveStatus: undefined as number | undefined,
    approveNo: undefined as string | undefined,
    approverId: undefined as number | undefined,
    approverName: undefined as string | undefined,
    approveDate: undefined as Date | undefined,
    deptId: undefined as number | undefined,
    empId: undefined as number | undefined,
    managerId: undefined as number | undefined,
    manger1Id: undefined as number | undefined,
    accountantId: undefined as number | undefined,
    checkerId: undefined as number | undefined,
  }
  formRef.value?.resetFields()
}
</script>
